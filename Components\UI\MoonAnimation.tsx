import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface MoonAnimationProps {
  isNightMode: boolean; // true quand mode = 'night'
  currentMode: string;  // pour détecter les changements de mode
}

const MoonAnimation: React.FC<MoonAnimationProps> = ({ isNightMode, currentMode }) => {
  const moonRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const fadeOutRef = useRef<gsap.core.Tween | null>(null);

  useEffect(() => {
    if (!moonRef.current) return;

    // 🌙 CISCO: Mode Nuit profonde - Apparition et descente de la lune
    if (isNightMode && currentMode === 'night') {
      console.log('🌙 Mode Nuit profonde activé - Animation de la lune');

      // Arrêter toute animation en cours
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }

      // Position initiale : tout en haut, au centre, invisible
      gsap.set(moonRef.current, {
        x: '50vw',
        y: '-100px',
        xPercent: -50,
        opacity: 0,
        scale: 1,
        display: 'block'
      });

      // Créer la timeline d'animation
      animationRef.current = gsap.timeline();

      // Phase 1: Apparition douce (3 secondes)
      animationRef.current.to(moonRef.current, {
        opacity: 0.8,
        duration: 3,
        ease: "power2.out"
      });

      // Phase 2: Descente très lente sur 5 minutes (300 pixels) - 🔧 CISCO: Ralentie considérablement
      animationRef.current.to(moonRef.current, {
        y: '300px',
        duration: 300, // 5 minutes au lieu d'1 minute - beaucoup plus lent
        ease: "none" // Mouvement linéaire et constant
      }, "-=1"); // Commence 1 seconde avant la fin de l'apparition

      console.log('🌙 Animation de descente lancée (300 secondes - 5 minutes)');

    } else if (!isNightMode) {
      // 🌅 CISCO: Autre mode - Disparition de la lune
      console.log(`🌅 Mode ${currentMode} activé - Disparition de la lune`);

      // Arrêter l'animation de descente
      if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }

      // Si la lune est visible, la faire disparaître en douceur
      if (moonRef.current && gsap.getProperty(moonRef.current, "opacity") > 0) {
        fadeOutRef.current = gsap.to(moonRef.current, {
          opacity: 0,
          duration: 4, // Disparition sur 4 secondes
          ease: "power2.in",
          onComplete: () => {
            if (moonRef.current) {
              gsap.set(moonRef.current, { display: 'none' });
            }
            console.log('🌙 Lune disparue');
          }
        });
      } else {
        // Si déjà invisible, juste la cacher
        gsap.set(moonRef.current, { display: 'none' });
      }
    }

    // Nettoyage au démontage
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }
    };
  }, [isNightMode, currentMode]);

  return (
    <div
      ref={moonRef}
      className="fixed top-0 left-0 pointer-events-none"
      style={{
        zIndex: 5, // 🔧 CISCO: Entre les étoiles (z-1) et les nuages (z-10-12) - derrière tous les nuages
        display: 'none', // Initialement cachée
        width: '180px', // 🔧 CISCO: Plus grosse (120px -> 180px)
        height: '180px', // 🔧 CISCO: Plus grosse (120px -> 180px)
        backgroundImage: 'url(/Lune-Moon.png)',
        backgroundSize: 'contain',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        filter: 'drop-shadow(0 0 30px rgba(255, 255, 255, 0.4))', // Halo lunaire plus visible
      }}
      title="🌙 Lune nocturne"
    />
  );
};

export default MoonAnimation;
